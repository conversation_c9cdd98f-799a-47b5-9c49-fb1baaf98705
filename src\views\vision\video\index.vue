<template>
  <div class="video-generation-container text-white">
    <!-- 左侧控制面板 -->
    <div class="control-panel">
      <div class="text-white text-14px font-bold">视频描述</div>

      <!-- 主体输入 -->
      <div class="form-group">
        <span>主体</span>
        <textarea
          v-model="formData.mainSubject"
          type="text"
          placeholder="例如：女生"
          class="form-input"
        ></textarea>
      </div>

      <!-- 场景输入 -->
      <div class="form-group">
        <span>场景</span>
        <div class="textarea-container">
          <textarea
            v-model="formData.scene"
            placeholder="例如：黄昏全身侧影交叉双臂站立，逆光下淡黄色的暖子流淌"
            class="form-textarea"
            rows="4"
          ></textarea>
          <div class="ai-optimize" @click="handleAiOptimize">
            <div>
              <Icon :size="14" icon="svg-icon:magic" />
              <span class="ai-text ml-1">AI 优化</span>
            </div>
            <span class="ai-count">1/500</span>
          </div>
        </div>
      </div>

      <h3>参数设置</h3>

      <!-- 视频比例 -->
      <div class="form-group">
        <label>视频比例</label>
        <div class="radio-group">
          <div class="radio-item">
            <input
              id="ratio-9-16"
              v-model="formData.aspectRatio"
              type="radio"
              value="9:16"
              class="radio-input"
            />
            <label for="ratio-9-16" class="radio-label">9:16</label>
          </div>
          <div class="radio-item">
            <input
              id="ratio-16-9"
              v-model="formData.aspectRatio"
              type="radio"
              value="16:9"
              class="radio-input"
            />
            <label for="ratio-16-9" class="radio-label">16:9</label>
          </div>
        </div>
      </div>

      <!-- 生成数量 -->
      <div class="form-group">
        <label>生成数量</label>
        <div class="radio-group">
          <div class="radio-item">
            <input
              id="count-1"
              v-model="formData.count"
              type="radio"
              :value="1"
              class="radio-input"
            />
            <label for="count-1" class="radio-label">1条</label>
          </div>
          <div class="radio-item">
            <input
              id="count-2"
              v-model="formData.count"
              type="radio"
              :value="2"
              class="radio-input"
            />
            <label for="count-2" class="radio-label">2条</label>
          </div>
        </div>
      </div>

      <!-- 生成时长 -->
      <div class="form-group">
        <label>生成时长</label>
        <div class="radio-group">
          <div class="radio-item">
            <input
              id="duration-5s"
              v-model="formData.duration"
              type="radio"
              value="5s"
              class="radio-input"
            />
            <label for="duration-5s" class="radio-label">5S</label>
          </div>
          <div class="radio-item">
            <input
              id="duration-10s"
              v-model="formData.duration"
              type="radio"
              value="10s"
              class="radio-input"
            />
            <label for="duration-10s" class="radio-label">10S</label>
          </div>
        </div>
      </div>

      <button @click="handleGenerate" :disabled="isLoading || isGenerating" class="generate-btn">
        <span v-if="!isLoading && !isGenerating">立即生成</span>
        <span v-else-if="isGenerating && formData.count > 1"
          >生成中 {{ currentGenerationIndex + 1 }}/{{ formData.count }}</span
        >
        <span v-else>生成中...</span>
      </button>
    </div>

    <div class="result-panel">
      <div v-if="error" class="error-state">
        <p>出错了：{{ error }}</p>
      </div>
      <div v-else-if="currentDisplayVideo || isGenerating" class="video-display">
        <div v-if="currentDisplayVideo" class="current-video-wrapper">
          <video
            :src="currentDisplayVideo.url || currentDisplayVideo"
            controls
            preload="metadata"
            class="current-video"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
        <div v-else-if="isGenerating" class="generating-placeholder">
          <div class="placeholder-content">
            <div class="spinner"></div>
            <p>正在生成视频...</p>
          </div>
        </div>
      </div>
      <div v-else class="initial-state">
        <img src="@/assets/imgs/vision/frame.png" alt="请在左侧输入描述并设置参数" />
        <p>告诉我你的想法，简单一“点”即可实现</p>
      </div>

      <!-- 多条生成时的loading指示器 -->
      <div
        v-if="(isGenerating || showCompletedProgress) && formData.count > 1"
        class="generation-progress"
      >
        <div class="progress-boxes">
          <div
            v-for="n in formData.count"
            :key="n"
            class="progress-box"
            :class="{
              completed:
                (isGenerating && n <= currentGenerationIndex) ||
                (!isGenerating && generatedVideos[n - 1]),
              loading: isGenerating && n === currentGenerationIndex + 1,
              pending:
                (isGenerating && n > currentGenerationIndex + 1) ||
                (!isGenerating && !generatedVideos[n - 1]),
              clickable: generatedVideos[n - 1],
              active:
                generatedVideos[n - 1] &&
                currentDisplayVideo &&
                (generatedVideos[n - 1].url || generatedVideos[n - 1]) ===
                  (currentDisplayVideo.url || currentDisplayVideo)
            }"
            @click="generatedVideos[n - 1] && selectProgressVideo(n - 1)"
          >
            <!-- 已完成的显示视频预览 -->
            <video
              v-if="generatedVideos[n - 1]"
              :src="generatedVideos[n - 1].url || generatedVideos[n - 1]"
              :alt="`生成的视频 ${n}`"
              class="progress-preview"
              muted
              preload="metadata"
            ></video>
            <!-- 正在生成的显示loading -->
            <div v-else-if="n === currentGenerationIndex + 1" class="box-spinner"></div>
          </div>
        </div>
        <p class="progress-text">
          <span v-if="isGenerating"
            >正在生成第 {{ currentGenerationIndex + 1 }} / {{ formData.count }} 个视频...</span
          >
          <span v-else>已完成 {{ generatedVideos.length }} / {{ formData.count }} 个视频生成</span>
        </p>
      </div>

      <!-- 单条生成时的loading -->
      <div v-else-if="isGenerating" class="loading-state">
        <div class="spinner"></div>
        <p>正在生成视频，请稍候...</p>
      </div>

      <!-- 显示历史面板按钮 -->
      <button
        v-if="historyVideosList.length > 0"
        @click="showHistoryPanel = true"
        class="history-toggle-btn"
      >
        视频列表 ({{ historyVideosList.length }})
      </button>
    </div>

    <!-- 右侧历史面板 -->
    <div class="history-panel" :class="{ show: showHistoryPanel }">
      <div class="history-header">
        <h3>视频列表</h3>
        <div class="history-controls">
          <button @click="scrollHistory('up')" :disabled="!canScrollUp" class="scroll-btn">
            ↑
          </button>
          <button @click="scrollHistory('down')" :disabled="!canScrollDown" class="scroll-btn">
            ↓
          </button>
          <button @click="showHistoryPanel = false" class="close-btn">×</button>
        </div>
      </div>
      <div class="history-content" ref="historyContainer" @scroll="updateScrollState">
        <div
          v-for="(video, index) in historyVideosList"
          :key="index"
          class="history-video-wrapper"
          :class="{
            active:
              currentDisplayVideo &&
              (video.url || video) === (currentDisplayVideo.url || currentDisplayVideo)
          }"
          @click="selectHistoryVideo(video)"
        >
          <video
            :src="video.url || video"
            :alt="`历史视频 ${index + 1}`"
            muted
            preload="metadata"
          ></video>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { VideoApi } from '@/api/ai/video'
import { difyApi } from '@/api/ai/workflow'

// 表单数据
const formData = ref({
  mainSubject: '',
  scene: '',
  aspectRatio: '16:9',
  count: 1,
  duration: '5s'
})

// 历史视频列表
const historyVideosList = ref([])
const showHistoryPanel = ref(false)
const historyContainer = ref(null)
const canScrollUp = ref(false)
const canScrollDown = ref(false)

// 其他状态
const isLoading = ref(false)
const isGenerating = ref(false)
const currentGenerationIndex = ref(0)
const showCompletedProgress = ref(false)
const error = ref(null)
const generatedVideos = ref([])
const currentDisplayVideo = ref(null) // 当前显示的视频

// 拼接完整的 prompt
const fullPrompt = computed(() => {
  return `${formData.value.mainSubject}, ${formData.value.scene}`
})

// 单次生成视频的函数
const generateSingleVideo = async () => {
  const requestBody = {
    inputs: {
      input: fullPrompt.value,
      ratio: formData.value.aspectRatio,
      Duration: formData.value.duration
    },
    query: fullPrompt.value,
    user: 'vue-user-123',
    response_mode: 'blocking',
    tool_parameters: {
      video_generation_ratio: formData.value.aspectRatio === '16:9' ? '1024x576' : '576x1024',
      video_generation_duration: formData.value.duration,
      video_generation_n: 1 // 每次只生成一个
    }
  }

  try {
    const result = await VideoApi.difyGenerateVideo(requestBody)

    if (
      result.data &&
      result.data.outputs &&
      result.data.outputs.json &&
      result.data.outputs.json.length
    ) {
      return {
        url: result.data.outputs.json[0].url
        // prompt: fullPrompt.value,
        // timestamp: new Date().toISOString()
      }
    } else if (result.answer) {
      throw new Error(`生成失败: ${result.answer}`)
    } else {
      throw new Error('API 返回的数据格式不正确，未能找到视频。')
    }
  } catch (error) {
    console.error('视频生成API调用失败:', error)
    throw error
  }
}

// 主生成函数
const handleGenerate = async () => {
  if (!formData.value.mainSubject && !formData.value.scene) {
    alert('主词和场景至少需要填写一个！')
    return
  }

  isLoading.value = true
  isGenerating.value = true
  currentGenerationIndex.value = 0
  showCompletedProgress.value = false
  error.value = null
  generatedVideos.value = []
  currentDisplayVideo.value = null

  try {
    const totalCount = formData.value.count

    for (let i = 0; i < totalCount; i++) {
      currentGenerationIndex.value = i

      try {
        const videoData = await generateSingleVideo()

        // 添加到当前显示的视频列表
        generatedVideos.value.push(videoData)

        // 设置为当前显示的视频（总是显示最新生成的）
        currentDisplayVideo.value = videoData

        // 添加到历史列表
        historyVideosList.value.push(videoData)

        // 更新滚动状态
        await nextTick()
        updateScrollState()
      } catch (singleError) {
        console.error(`生成第 ${i + 1} 个视频失败:`, singleError)
        // 继续生成下一个，不中断整个流程
      }
    }
  } catch (err) {
    error.value = err.message
    console.error(err)
  } finally {
    isLoading.value = false
    isGenerating.value = false

    // 如果生成了多个视频，显示完成的进度条3秒钟
    if (formData.value.count > 1 && generatedVideos.value.length > 0) {
      showCompletedProgress.value = true
      setTimeout(() => {
        showCompletedProgress.value = false
      }, 3000)
    }

    currentGenerationIndex.value = 0
  }
}

// 历史视频列表相关方法
const scrollHistory = (direction) => {
  if (!historyContainer.value) return

  const scrollAmount = 200
  if (direction === 'up') {
    historyContainer.value.scrollTop -= scrollAmount
  } else {
    historyContainer.value.scrollTop += scrollAmount
  }
}

const updateScrollState = () => {
  if (!historyContainer.value) return

  const { scrollTop, scrollHeight, clientHeight } = historyContainer.value
  canScrollUp.value = scrollTop > 0
  canScrollDown.value = scrollTop < scrollHeight - clientHeight - 1
}

const selectHistoryVideo = (video) => {
  // 点击历史视频时，将其设置为当前显示的视频
  currentDisplayVideo.value = video
}

const selectProgressVideo = (index) => {
  // 点击进度框中的视频时，将其设置为当前显示的视频
  if (generatedVideos.value[index]) {
    currentDisplayVideo.value = generatedVideos.value[index]
  }
}

// AI优化功能
async function handleAiOptimize() {
  const params = {
    inputs: {
      type: 'video',
      title: formData.value.mainSubject,
      scene: formData.value.scene
    },
    user: 'user-123'
  }
  const result = await difyApi.runWorkflow(params, import.meta.env.VITE_DIFY_Scene_API_KEY)
  formData.value.mainSubject = result.data.outputs.text
}
</script>

<style scoped>
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 主容器 */
.video-generation-container {
  display: flex;
  height: calc(100vh - 160px);
}

/* 左侧控制面板 */
.control-panel {
  width: 296px;
  padding: 16px;
  overflow-y: auto;
  background-color: rgb(255 255 255 / 5%);
  border-right: 1px solid #2d3748;
  border-radius: 8px;
}

.control-panel h2 {
  margin: 0 0 24px;
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  background: rgb(255 255 255 / 5%);
  border-radius: 8px;
}

.control-panel h3 {
  margin: 32px 0 16px;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

/* 表单组件 */
.form-group {
  margin-bottom: 24px;
}

.form-group span {
  width: 50px;
  padding-top: 8px;
  font-size: 14px;
  color: var(--text-secondary-color);
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  color: #fff;
  background: transparent;
  border: 1px solid #2d3748;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #6c5ce7;
  outline: none;
  box-shadow: 0 0 0 3px rgb(108 92 231 / 10%);
}

.form-textarea {
  min-height: 80px;
  padding-bottom: 40px; /* 为AI优化按钮留出空间 */
  resize: vertical;
}

/* 文本域容器 */
.textarea-container {
  position: relative;
  flex-grow: 1;
  width: 100%;
}

/* AI优化按钮样式 */
.ai-optimize {
  position: absolute;
  right: 0;
  bottom: 8px;
  left: 0;
  display: flex;
  height: 32px;
  padding: 0 12px;
  cursor: pointer;
  border-radius: 6px;
  align-items: center;
  gap: 6px;
  justify-content: space-between;
}

.ai-optimize .ai-icon {
  display: flex;
  width: 16px;
  height: 16px;
  color: white;
  align-items: center;
  justify-content: center;
}

.ai-optimize .ai-text {
  font-size: 12px;
  font-weight: 600;
  color: transparent;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  background-clip: text;
}

.ai-optimize .ai-count {
  font-size: 10px;
  color: rgb(255 255 255 / 80%);
}

/* 工具类 */
.ml-1 {
  margin-left: 4px;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.radio-item {
  position: relative;
}

.radio-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
}

.radio-label {
  display: inline-block;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #b2b2b2;
  cursor: pointer;
  background: #16213e;
  border: 1px solid #2d3748;
  border-radius: 20px;
  transition: all 0.3s ease;
  user-select: none;
}

.radio-input:checked + .radio-label {
  color: white;
  background: #6c5ce7;
  border-color: #6c5ce7;
  box-shadow: 0 2px 8px rgb(108 92 231 / 30%);
}

.radio-label:hover {
  color: #fff;
  border-color: #6c5ce7;
}

/* 生成按钮 */
.generate-btn {
  position: relative;
  width: 100%;
  padding: 16px 24px;
  overflow: hidden;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: white;
  cursor: pointer;
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(108 92 231 / 30%);
  transition: all 0.3s ease;
}

.generate-btn::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgb(255 255 255 / 20%), transparent);
  content: '';
  transition: left 0.5s;
}

.generate-btn:hover::before {
  left: 100%;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgb(108 92 231 / 40%);
}

.generate-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

/* 右侧结果区 */
.result-panel {
  display: flex;
  padding: 40px;
  margin-left: 16px;
  background-color: rgb(255 255 255 / 5%);
  border-radius: 8px;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
}

.error-state {
  text-align: center;
}

.error-state p {
  color: #ff6b6b;
}

/* 单视频显示区域 */
.video-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.current-video-wrapper {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
}

.current-video {
  display: block;
  width: 100%;
  height: 100%;
}

/* 生成中的占位符 */
.generating-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.placeholder-content {
  color: #b2b2b2;
  text-align: center;
}

.placeholder-content .spinner {
  margin: 0 auto 16px;
}

.placeholder-content p {
  margin: 0;
  font-size: 16px;
}

/* 加载动画 */
.loading-state {
  color: #b2b2b2;
  text-align: center;
}

.loading-state .spinner {
  margin: 0 auto 16px;
}

.loading-state p {
  margin: 0;
  font-size: 16px;
}

.initial-state {
  color: #aeb9e1;
  text-align: center;
}

.initial-state p {
  margin: 0;
  font-size: 18px;
}

/* 旋转动画 */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgb(108 92 231 / 30%);
  border-top: 4px solid #6c5ce7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 多条生成进度指示器 */
.generation-progress {
  position: absolute;
  bottom: 20px;
  left: 50%;
  z-index: 10;
  text-align: center;
  transform: translateX(-50%);
}

.progress-boxes {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 12px;
}

.progress-box {
  position: relative;
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
}

.progress-box.completed {
  overflow: hidden;
  background: linear-gradient(135deg, #675dff 0%, #bf78e6 100%);
  box-shadow: 0 4px 12px rgb(103 93 255 / 40%);
}

.progress-box.loading {
  background: rgb(103 93 255 / 20%);
  border: 2px solid #675dff;
}

.progress-box.pending {
  background: rgb(255 255 255 / 10%);
  border: 2px solid rgb(255 255 255 / 30%);
}

.progress-box.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.progress-box.clickable:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgb(103 93 255 / 60%);
}

.progress-box.active {
  border: 3px solid #675dff !important;
  box-shadow: 0 0 0 2px rgb(103 93 255 / 30%) !important;
}

.box-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgb(103 93 255 / 30%);
  border-top: 2px solid #675dff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.progress-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.progress-text {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

/* 历史面板 */
.history-panel {
  position: fixed;
  top: 0;
  right: -200px;
  z-index: 1000;
  width: 200px;
  height: 100vh;
  background: #0f1419;
  border-left: 1px solid #2d3748;
  transition: right 0.3s ease;
}

.history-panel.show {
  right: 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #2d3748;
}

.history-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.history-controls {
  display: flex;
  gap: 8px;
}

.scroll-btn,
.close-btn {
  display: flex;
  width: 24px;
  height: 24px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background: #16213e;
  border: 1px solid #2d3748;
  border-radius: 4px;
  transition: all 0.2s ease;
  align-items: center;
  justify-content: center;
}

.scroll-btn:hover,
.close-btn:hover {
  background: #6c5ce7;
  border-color: #6c5ce7;
}

.scroll-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.history-content {
  height: calc(100vh - 60px);
  padding: 8px;
  overflow-y: auto;
}

.history-content::-webkit-scrollbar {
  width: 4px;
}

.history-content::-webkit-scrollbar-track {
  background: #16213e;
}

.history-content::-webkit-scrollbar-thumb {
  background: #2d3748;
  border-radius: 2px;
}

.history-content::-webkit-scrollbar-thumb:hover {
  background: #6c5ce7;
}

.history-video-wrapper {
  position: relative;
  width: 100%;
  padding-top: 100%; /* 1:1 Aspect Ratio */
  margin-bottom: 8px;
  overflow: hidden;
  cursor: pointer;
  background-color: #16213e;
  border-radius: 8px;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
}

.history-video-wrapper:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgb(108 92 231 / 30%);
}

.history-video-wrapper.active {
  border: 3px solid #675dff;
  box-shadow: 0 0 0 2px rgb(103 93 255 / 30%);
}

.history-video-wrapper video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 调整主内容区域 */
.result-panel {
  position: relative;
}

/* 历史面板切换按钮 */
.history-toggle-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1001;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  cursor: pointer;
  background: #0f1419;
  border: 1px solid #2d3748;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.history-toggle-btn:hover {
  background: #6c5ce7;
  border-color: #6c5ce7;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgb(108 92 231 / 30%);
}
</style>
